import os
import shutil
import re
import platform

# 颜色输出类
class ColorPrint:
    """Windows兼容的彩色输出类"""

    def __init__(self):
        self.is_windows = platform.system() == "Windows"
        if self.is_windows:
            try:
                # 启用Windows控制台颜色支持
                import ctypes
                kernel32 = ctypes.windll.kernel32
                kernel32.SetConsoleMode(kernel32.GetStdHandle(-11), 7)
            except:
                pass

    def success(self, message):
        """绿色 - 成功信息"""
        if self.is_windows:
            print(f"\033[92m{message}\033[0m")
        else:
            print(f"\033[92m{message}\033[0m")

    def error(self, message):
        """红色 - 错误信息"""
        if self.is_windows:
            print(f"\033[91m{message}\033[0m")
        else:
            print(f"\033[91m{message}\033[0m")

    def warning(self, message):
        """黄色 - 警告信息"""
        if self.is_windows:
            print(f"\033[93m{message}\033[0m")
        else:
            print(f"\033[93m{message}\033[0m")

    def info(self, message):
        """蓝色 - 信息"""
        if self.is_windows:
            print(f"\033[94m{message}\033[0m")
        else:
            print(f"\033[94m{message}\033[0m")

    def normal(self, message):
        """普通输出"""
        print(message)

# 全局颜色输出实例
color_print = ColorPrint()

NAS_DIRECTORIES = {
    "1": {"name": "主板出货", "path": r"Y:\主板出货"},
    "2": {"name": "客户机", "path": r"Y:\Image\客户机"},
    "3": {"name": "贴背胶", "path": r"Y:\贴背胶"},
    "4": {"name": "后端外观", "path": r"Y:\后端外观"},
    "5": {"name": "整机数据验证", "path": r"Y:\整机数据验证"},
    "6": {"name": "回收站", "path": r"Y:\#recycle"}
}

def create_year_month_folders(target_path):
    color_print.info(f"在 {target_path} 创建年月文件夹结构...")
    for year in range(2023, 2026):
        year_path = os.path.join(target_path, str(year))
        if not os.path.exists(year_path):
            os.makedirs(year_path)
            color_print.success(f"创建文件夹: {year}")

        for month in range(1, 13):
            month_path = os.path.join(year_path, f"{month:02d}")
            if not os.path.exists(month_path):
                os.makedirs(month_path)
                color_print.success(f"创建文件夹: {year}/{month:02d}")

def delete_old_files(target_path, delete_year, folder_name=""):
    color_print.warning(f"现在开始删除 {folder_name}文件夹中的{delete_year}年文件，请勿操作计算机....")
    deleted_count = 0
    try:
        for filename in os.listdir(target_path):
            if (f"_{delete_year}" in filename and
                (filename.lower().endswith('.jpg') or filename.lower().endswith('.png') or
                 filename.lower().endswith('.jpeg') or filename.lower().endswith('.bmp'))):
                file_path = os.path.join(target_path, filename)
                try:
                    os.remove(file_path)
                    deleted_count += 1
                    if deleted_count % 1000 == 0:
                        color_print.info(f"已删除 {deleted_count} 个{folder_name}文件夹中的{delete_year}年文件...")
                except Exception as e:
                    color_print.error(f"删除文件失败: {filename}, 错误: {e}")
    except Exception as e:
        color_print.error(f"访问目录失败: {e}")

    color_print.success(f"恭喜你！共删除 {deleted_count} 个{folder_name}文件夹中的{delete_year}年文件")

def organize_photos_by_date(target_path, skip_year=None):
    color_print.info(f"现在开始整理 {target_path.split(':')[1]}文件夹中的的照片...")
    moved_count = 0
    error_count = 0

    try:
        files = os.listdir(target_path)
        # 统计图片文件
        total_files = len([f for f in files if f.lower().endswith(('.jpg', '.png', '.jpeg', '.bmp'))])
        color_print.info(f"发现 {total_files} 个图片文件需要处理")

        for filename in files:
            # 检查是否为图片格式
            if not filename.lower().endswith(('.jpg', '.png', '.jpeg', '.bmp')):
                continue

            # 主板出货: 9XTFUT218G013776_admin_20220219220351.jpg
            # 客户机: 签收拍照_ACTHVB2309001339_13327_20241017202525197.png
            date_match = re.search(r'_(\d{8})', filename)  # 8位
            if not date_match:
                date_match = re.search(r'_(\d{14})', filename)  # 14位

            if date_match:
                date_str = date_match.group(1)
                if len(date_str) >= 8:
                    year = date_str[:4]
                    month = date_str[4:6]

                    # 跳过指定年份的文件
                    if skip_year and year == skip_year:
                        continue

                    # 构建目标路径
                    target_folder = os.path.join(target_path, year, month)

                    # 确保目标文件夹存在
                    if not os.path.exists(target_folder):
                        os.makedirs(target_folder)

                    # 移动文件
                    source_file = os.path.join(target_path, filename)
                    target_file = os.path.join(target_folder, filename)

                    try:
                        # 检查文件是否在根目录
                        if os.path.dirname(source_file) == target_path:
                            shutil.move(source_file, target_file)
                            moved_count += 1

                            if moved_count % 1000 == 0:
                                color_print.info(f"已移动 {moved_count} 个文件...")

                    except Exception as e:
                        color_print.error(f"移动文件失败: {filename}, 错误: {e}")
                        error_count += 1
            else:
                error_count += 1

    except Exception as e:
        color_print.error(f"处理过程中出错: {e}")

    color_print.success(f"恭喜你！成功移动: {moved_count} 个文件，失败: {error_count} 个")

def clear_recycle_bin(recycle_path):
    """清空回收站功能"""
    color_print.warning(f"现在开始清空回收站 {recycle_path}，请勿操作计算机....")
    deleted_count = 0
    deleted_size = 0

    try:
        if not os.path.exists(recycle_path):
            color_print.error(f"回收站路径不存在: {recycle_path}")
            return

        # 遍历回收站中的所有文件和文件夹
        for root, dirs, files in os.walk(recycle_path, topdown=False):
            # 删除文件
            for filename in files:
                file_path = os.path.join(root, filename)
                try:
                    file_size = os.path.getsize(file_path)
                    os.remove(file_path)
                    deleted_count += 1
                    deleted_size += file_size

                    if deleted_count % 100 == 0:
                        color_print.info(f"已删除 {deleted_count} 个文件...")

                except Exception as e:
                    color_print.error(f"删除文件失败: {filename}, 错误: {e}")

            # 删除空文件夹
            for dirname in dirs:
                dir_path = os.path.join(root, dirname)
                try:
                    os.rmdir(dir_path)
                except Exception as e:
                    color_print.error(f"删除文件夹失败: {dirname}, 错误: {e}")

    except Exception as e:
        color_print.error(f"清空回收站过程中出错: {e}")

    # 转换文件大小为可读格式
    size_mb = deleted_size / (1024 * 1024)
    size_gb = size_mb / 1024

    if size_gb >= 1:
        size_str = f"{size_gb:.2f} GB"
    else:
        size_str = f"{size_mb:.2f} MB"

    color_print.success(f"恭喜你！回收站清空完成，共删除 {deleted_count} 个文件，释放空间: {size_str}")

def select_directory():
    color_print.info("\n=== 可用目录 ===")
    for key, config in NAS_DIRECTORIES.items():
        color_print.normal(f"{key}. {config['name']} ({config['path']})")

    choice = input(f"\n请选择目录 (1-{len(NAS_DIRECTORIES)}): ")

    if choice in NAS_DIRECTORIES:
        return choice
    else:
        color_print.error("无效选项")
        return None

def execute_operations(directory_key, operations, delete_year=None):
    config = NAS_DIRECTORIES[directory_key]
    target_path = config["path"]

    color_print.info(f"\n开始处理目录: {config['name']}")

    if not os.path.exists(target_path):
        color_print.error(f"错误: 无法访问路径 {target_path}，请检查网络连接和权限")
        color_print.warning("请先登陆NAS，然后再运行本程序")
        return

    if "create_folders" in operations:
        create_year_month_folders(target_path)

    if "delete_old" in operations and delete_year:
        confirm = input(f"确认删除所有{delete_year}年文件吗? (输入'YES'确认): ")
        if confirm == "YES":
            delete_old_files(target_path, delete_year, config['name'])
        else:
            color_print.warning(f"删除{delete_year}年文件操作已取消")

    if "organize_photos" in operations:
        organize_photos_by_date(target_path, delete_year)

    if "clear_recycle" in operations:
        confirm = input("确认清空回收站吗? 此操作不可恢复! (输入'YES'确认): ")
        if confirm == "YES":
            clear_recycle_bin(target_path)
        else:
            color_print.warning("清空回收站操作已取消")

def main():
    color_print.info("=== NAS照片整理工具 ===")
    while True:
        directory_key = select_directory()
        if directory_key is None:
            continue

        config = NAS_DIRECTORIES[directory_key]
        color_print.success(f"\n已选择目录: {config['name']} 文件夹")

        # 如果选择的是回收站，显示特殊菜单
        if directory_key == "6":  # 回收站
            choice = input("""
请选择操作:
1. 清空回收站
2. 返回目录选择
3. 退出

请输入选项 (1-3): """)

            if choice == "1":
                execute_operations(directory_key, ["clear_recycle"])
            elif choice == "2":
                continue
            elif choice == "3":
                color_print.info("退出程序")
                break
            else:
                color_print.error("无效选项")
        else:
            choice = input("""
请选择操作:
1. 只创建年月文件夹结构
2. 只删除指定年份文件
3. 只整理现有照片到对应文件夹
4. 执行完整流程 (创建文件夹 + 删除指定年份 + 整理照片)
5. 返回目录选择
6. 退出

请输入选项 (1-6): """)

            delete_year = None
            if choice in ["2", "4"]:
                delete_year = input("请输入要删除的年份: ")

            if choice == "1":
                execute_operations(directory_key, ["create_folders"])
            elif choice == "2":
                execute_operations(directory_key, ["delete_old"], delete_year)
            elif choice == "3":
                execute_operations(directory_key, ["organize_photos"])
            elif choice == "4":
                execute_operations(directory_key, ["create_folders", "delete_old", "organize_photos"], delete_year)
            elif choice == "5":
                continue
            elif choice == "6":
                color_print.info("退出程序")
                break
            else:
                color_print.error("无效选项")

        continue_choice = input("\n是否继续操作其他目录? (y/n): ")
        if continue_choice.lower() != 'y':
            break

if __name__ == "__main__":
    main()